# 新的Task结构体字段说明

## 概述
本文档描述了Task结构体从原有的5个字段更新为12个字段的详细变更。新的字段结构更好地支持问题管理和任务跟踪功能。

## 字段变更对比

### 原有字段结构
```go
type Task struct {
    ID          int       `json:"id"`
    Title       string    `json:"title"`
    Description string    `json:"description"`
    Status      string    `json:"status"`
    CreatedAt   time.Time `json:"created_at"`
}
```

### 新的字段结构
```go
type Task struct {
    ID           int       `json:"id"`
    IssueNo      string    `json:"issue_no"`
    IssueSummary string    `json:"issue_summary"`
    AssignedTo   string    `json:"assigned_to"`
    Priority     string    `json:"priority"`
    Submitter    string    `json:"submitter"`
    SubmittedAt  time.Time `json:"submitted_at"`
    Baseline     string    `json:"baseline"`
    TaskStatus   string    `json:"task_status"`
    DelayDays    int       `json:"delay_days"`
    IssueLink    string    `json:"issue_link"`
    IssueStatus  string    `json:"issue_status"`
}
```

## 字段详细说明

| 字段名 | 数据类型 | JSON标签 | 描述 | 示例值 |
|--------|----------|----------|------|--------|
| ID | int | `id` | 任务唯一标识符 | 1 |
| IssueNo | string | `issue_no` | 问题编号 | "IDMS-2025-001" |
| IssueSummary | string | `issue_summary` | 问题摘要 | "系统登录功能优化" |
| AssignedTo | string | `assigned_to` | 问题处理人 | "张三" |
| Priority | string | `priority` | 问题级别 | "高", "中", "低" |
| Submitter | string | `submitter` | 问题提交人 | "李四" |
| SubmittedAt | time.Time | `submitted_at` | 问题提交时间 | "2025-01-01T09:30:00Z" |
| Baseline | string | `baseline` | 问题基线 | "v2.1.0" |
| TaskStatus | string | `task_status` | 任务状态 | "进行中", "已完成", "待处理" |
| DelayDays | int | `delay_days` | 任务滞留天数 | 3 |
| IssueLink | string | `issue_link` | 问题链接 | "https://jira.company.com/browse/IDMS-2025-001" |
| IssueStatus | string | `issue_status` | 问题状态 | "In Progress", "Closed", "Open" |

## 数据库表结构变更

### 新的数据库表结构
```sql
CREATE TABLE IF NOT EXISTS tasks (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    issue_no TEXT NOT NULL,
    issue_summary TEXT NOT NULL,
    assigned_to TEXT,
    priority TEXT,
    submitter TEXT,
    submitted_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    baseline TEXT,
    task_status TEXT NOT NULL,
    delay_days INTEGER DEFAULT 0,
    issue_link TEXT,
    issue_status TEXT
);
```

### 索引优化
为了提高查询性能，创建了以下索引：
- `idx_tasks_task_status` - 任务状态索引
- `idx_tasks_submitted_at` - 提交时间索引
- `idx_tasks_assigned_to` - 处理人索引

## API端点变更

所有API端点的请求和响应格式都已更新以支持新的字段结构：

### 创建任务 (POST /tasks)
```json
{
  "issue_no": "IDMS-2025-001",
  "issue_summary": "系统登录功能优化",
  "assigned_to": "张三",
  "priority": "高",
  "submitter": "李四",
  "submitted_at": "2025-01-01T09:30:00Z",
  "baseline": "v2.1.0",
  "task_status": "进行中",
  "delay_days": 3,
  "issue_link": "https://jira.company.com/browse/IDMS-2025-001",
  "issue_status": "In Progress"
}
```

### 响应格式
```json
{
  "id": 1,
  "issue_no": "IDMS-2025-001",
  "issue_summary": "系统登录功能优化",
  "assigned_to": "张三",
  "priority": "高",
  "submitter": "李四",
  "submitted_at": "2025-01-01T09:30:00Z",
  "baseline": "v2.1.0",
  "task_status": "进行中",
  "delay_days": 3,
  "issue_link": "https://jira.company.com/browse/IDMS-2025-001",
  "issue_status": "In Progress"
}
```

## 测试验证

提供了两个测试脚本来验证新的字段结构：

1. **PowerShell版本**: `test/api_test.ps1`
2. **Bash版本**: `test/api_test.sh`

### 运行测试
```bash
# 启动服务器
cd src
go run main.go

# 在另一个终端运行测试
# PowerShell
./test/api_test.ps1

# 或者 Bash
chmod +x ./test/api_test.sh
./test/api_test.sh
```

## 兼容性说明

⚠️ **重要提示**: 这是一个破坏性变更，与之前的API版本不兼容。

- 所有现有的客户端代码需要更新以使用新的字段名称
- 数据库表结构已完全重新设计
- 由于使用内存数据库，重启服务器后所有数据将丢失

## 迁移指南

如果需要从旧版本迁移到新版本：

1. 更新客户端代码以使用新的字段名称
2. 更新API调用以发送正确的JSON结构
3. 测试所有CRUD操作以确保兼容性
4. 更新任何依赖于旧字段名称的文档或配置

## 示例数据

参考 `test/sample_task.json` 文件查看完整的示例数据格式。
