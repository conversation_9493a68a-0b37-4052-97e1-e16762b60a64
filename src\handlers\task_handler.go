package handlers

import (
	"net/http"
	"strconv"

	"github.com/labstack/echo/v4"

	"idms_task_server/database"
	"idms_task_server/models"
)

func CreateTask(c echo.Context) error {
	var task models.Task
	if err := c.Bind(&task); err != nil {
		return c.JSON(http.StatusBadRequest, map[string]string{"error": "Invalid request payload"})
	}

	query := `INSERT INTO tasks (title, description, status) VALUES (?, ?, ?)`
	result, err := database.DB.Exec(query, task.Title, task.Description, task.Status)
	if err != nil {
		return c.JSON(http.StatusInternalServerError, map[string]string{"error": "Failed to create task"})
	}

	id, _ := result.LastInsertId()
	task.ID = int(id)
	return c.JSON(http.StatusCreated, task)
}

func GetTasks(c echo.Context) error {
	rows, err := database.DB.Query(`SELECT id, title, description, status, created_at FROM tasks`)
	if err != nil {
		return c.JSON(http.StatusInternalServerError, map[string]string{"error": "Failed to fetch tasks"})
	}
	defer rows.Close()

	tasks := []models.Task{}
	for rows.Next() {
		var task models.Task
		if err := rows.Scan(&task.ID, &task.Title, &task.Description, &task.Status, &task.CreatedAt); err != nil {
			return c.JSON(http.StatusInternalServerError, map[string]string{"error": "Failed to parse tasks"})
		}
		tasks = append(tasks, task)
	}

	return c.JSON(http.StatusOK, tasks)
}

func GetTaskByID(c echo.Context) error {
	id, err := strconv.Atoi(c.Param("id"))
	if err != nil {
		return c.JSON(http.StatusBadRequest, map[string]string{"error": "Invalid task ID"})
	}

	query := `SELECT id, title, description, status, created_at FROM tasks WHERE id = ?`
	row := database.DB.QueryRow(query, id)

	var task models.Task
	if err := row.Scan(&task.ID, &task.Title, &task.Description, &task.Status, &task.CreatedAt); err != nil {
		return c.JSON(http.StatusNotFound, map[string]string{"error": "Task not found"})
	}

	return c.JSON(http.StatusOK, task)
}

func UpdateTask(c echo.Context) error {
	id, err := strconv.Atoi(c.QueryParam("id"))
	if err != nil {
		return c.JSON(http.StatusBadRequest, map[string]string{"error": "Invalid task ID"})
	}

	var task models.Task
	if err := c.Bind(&task); err != nil {
		return c.JSON(http.StatusBadRequest, map[string]string{"error": "Invalid request payload"})
	}

	query := `UPDATE tasks SET title = ?, description = ?, status = ? WHERE id = ?`
	_, err = database.DB.Exec(query, task.Title, task.Description, task.Status, id)
	if err != nil {
		return c.JSON(http.StatusInternalServerError, map[string]string{"error": "Failed to update task"})
	}

	return c.NoContent(http.StatusOK)
}

func DeleteTask(c echo.Context) error {
	id, err := strconv.Atoi(c.QueryParam("id"))
	if err != nil {
		return c.JSON(http.StatusBadRequest, map[string]string{"error": "Invalid task ID"})
	}

	query := `DELETE FROM tasks WHERE id = ?`
	_, err = database.DB.Exec(query, id)
	if err != nil {
		return c.JSON(http.StatusInternalServerError, map[string]string{"error": "Failed to delete task"})
	}

	return c.NoContent(http.StatusOK)
}
