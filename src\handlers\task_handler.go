package handlers

import (
	"net/http"
	"strconv"

	"github.com/labstack/echo/v4"

	"idms_task_server/database"
	"idms_task_server/models"
)

func CreateTask(c echo.Context) error {
	var task models.Task
	if err := c.Bind(&task); err != nil {
		return c.JSON(http.StatusBadRequest, map[string]string{"error": "Invalid request payload"})
	}

	query := `INSERT INTO tasks (issue_no, issue_summary, assigned_to, priority, submitter, submitted_at, baseline, task_status, delay_days, issue_link, issue_status) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)`
	result, err := database.DB.Exec(query, task.IssueNo, task.IssueSummary, task.AssignedTo, task.Priority, task.Submitter, task.SubmittedAt, task.Baseline, task.TaskStatus, task.DelayDays, task.IssueLink, task.IssueStatus)
	if err != nil {
		return c.JSON(http.StatusInternalServerError, map[string]string{"error": "Failed to create task"})
	}

	id, _ := result.LastInsertId()
	task.ID = int(id)
	return c.JSON(http.StatusCreated, task)
}

func GetTasks(c echo.Context) error {
	rows, err := database.DB.Query(`SELECT id, issue_no, issue_summary, assigned_to, priority, submitter, submitted_at, baseline, task_status, delay_days, issue_link, issue_status FROM tasks`)
	if err != nil {
		return c.JSON(http.StatusInternalServerError, map[string]string{"error": "Failed to fetch tasks"})
	}
	defer rows.Close()

	tasks := []models.Task{}
	for rows.Next() {
		var task models.Task
		if err := rows.Scan(&task.ID, &task.IssueNo, &task.IssueSummary, &task.AssignedTo, &task.Priority, &task.Submitter, &task.SubmittedAt, &task.Baseline, &task.TaskStatus, &task.DelayDays, &task.IssueLink, &task.IssueStatus); err != nil {
			return c.JSON(http.StatusInternalServerError, map[string]string{"error": "Failed to parse tasks"})
		}
		tasks = append(tasks, task)
	}

	return c.JSON(http.StatusOK, tasks)
}

func GetTaskByID(c echo.Context) error {
	id, err := strconv.Atoi(c.Param("id"))
	if err != nil {
		return c.JSON(http.StatusBadRequest, map[string]string{"error": "Invalid task ID"})
	}

	query := `SELECT id, issue_no, issue_summary, assigned_to, priority, submitter, submitted_at, baseline, task_status, delay_days, issue_link, issue_status FROM tasks WHERE id = ?`
	row := database.DB.QueryRow(query, id)

	var task models.Task
	if err := row.Scan(&task.ID, &task.IssueNo, &task.IssueSummary, &task.AssignedTo, &task.Priority, &task.Submitter, &task.SubmittedAt, &task.Baseline, &task.TaskStatus, &task.DelayDays, &task.IssueLink, &task.IssueStatus); err != nil {
		return c.JSON(http.StatusNotFound, map[string]string{"error": "Task not found"})
	}

	return c.JSON(http.StatusOK, task)
}

func UpdateTask(c echo.Context) error {
	id, err := strconv.Atoi(c.QueryParam("id"))
	if err != nil {
		return c.JSON(http.StatusBadRequest, map[string]string{"error": "Invalid task ID"})
	}

	var task models.Task
	if err := c.Bind(&task); err != nil {
		return c.JSON(http.StatusBadRequest, map[string]string{"error": "Invalid request payload"})
	}

	query := `UPDATE tasks SET issue_no = ?, issue_summary = ?, assigned_to = ?, priority = ?, submitter = ?, submitted_at = ?, baseline = ?, task_status = ?, delay_days = ?, issue_link = ?, issue_status = ? WHERE id = ?`
	_, err = database.DB.Exec(query, task.IssueNo, task.IssueSummary, task.AssignedTo, task.Priority, task.Submitter, task.SubmittedAt, task.Baseline, task.TaskStatus, task.DelayDays, task.IssueLink, task.IssueStatus, id)
	if err != nil {
		return c.JSON(http.StatusInternalServerError, map[string]string{"error": "Failed to update task"})
	}

	return c.NoContent(http.StatusOK)
}

func DeleteTask(c echo.Context) error {
	id, err := strconv.Atoi(c.QueryParam("id"))
	if err != nil {
		return c.JSON(http.StatusBadRequest, map[string]string{"error": "Invalid task ID"})
	}

	query := `DELETE FROM tasks WHERE id = ?`
	_, err = database.DB.Exec(query, id)
	if err != nil {
		return c.JSON(http.StatusInternalServerError, map[string]string{"error": "Failed to delete task"})
	}

	return c.NoContent(http.StatusOK)
}
