# Task Management API 测试脚本
# 测试新的字段结构

Write-Host "开始测试 Task Management API..." -ForegroundColor Green

# 设置API基础URL
$baseUrl = "http://localhost:8080"

# 测试健康检查
Write-Host "`n1. 测试健康检查..." -ForegroundColor Yellow
try {
    $healthResponse = Invoke-RestMethod -Uri "$baseUrl/health" -Method GET
    Write-Host "健康检查成功: $($healthResponse.status)" -ForegroundColor Green
    Write-Host "数据库类型: $($healthResponse.database)" -ForegroundColor Cyan
} catch {
    Write-Host "健康检查失败: $($_.Exception.Message)" -ForegroundColor Red
    exit 1
}

# 测试创建任务
Write-Host "`n2. 测试创建任务..." -ForegroundColor Yellow
$newTask = @{
    issue_no = "ISSUE-001"
    issue_summary = "测试问题摘要"
    assigned_to = "张三"
    priority = "高"
    submitter = "李四"
    submitted_at = "2025-01-01T10:00:00Z"
    baseline = "v1.0.0"
    task_status = "进行中"
    delay_days = 0
    issue_link = "https://example.com/issue/001"
    issue_status = "打开"
} | ConvertTo-Json

try {
    $createResponse = Invoke-RestMethod -Uri "$baseUrl/tasks" -Method POST -Body $newTask -ContentType "application/json"
    Write-Host "任务创建成功，ID: $($createResponse.id)" -ForegroundColor Green
    $taskId = $createResponse.id
    
    # 验证返回的字段
    Write-Host "验证返回字段:" -ForegroundColor Cyan
    Write-Host "  问题编号: $($createResponse.issue_no)"
    Write-Host "  问题摘要: $($createResponse.issue_summary)"
    Write-Host "  处理人: $($createResponse.assigned_to)"
    Write-Host "  优先级: $($createResponse.priority)"
    Write-Host "  提交人: $($createResponse.submitter)"
    Write-Host "  任务状态: $($createResponse.task_status)"
    Write-Host "  滞留天数: $($createResponse.delay_days)"
    Write-Host "  问题链接: $($createResponse.issue_link)"
    Write-Host "  问题状态: $($createResponse.issue_status)"
} catch {
    Write-Host "创建任务失败: $($_.Exception.Message)" -ForegroundColor Red
    exit 1
}

# 测试获取所有任务
Write-Host "`n3. 测试获取所有任务..." -ForegroundColor Yellow
try {
    $allTasksResponse = Invoke-RestMethod -Uri "$baseUrl/tasks" -Method GET
    Write-Host "获取任务列表成功，共 $($allTasksResponse.Count) 个任务" -ForegroundColor Green
    
    if ($allTasksResponse.Count -gt 0) {
        $firstTask = $allTasksResponse[0]
        Write-Host "第一个任务详情:" -ForegroundColor Cyan
        Write-Host "  ID: $($firstTask.id)"
        Write-Host "  问题编号: $($firstTask.issue_no)"
        Write-Host "  问题摘要: $($firstTask.issue_summary)"
        Write-Host "  处理人: $($firstTask.assigned_to)"
        Write-Host "  任务状态: $($firstTask.task_status)"
    }
} catch {
    Write-Host "获取任务列表失败: $($_.Exception.Message)" -ForegroundColor Red
    exit 1
}

# 测试根据ID获取任务
Write-Host "`n4. 测试根据ID获取任务..." -ForegroundColor Yellow
try {
    $taskResponse = Invoke-RestMethod -Uri "$baseUrl/tasks/$taskId" -Method GET
    Write-Host "获取任务成功，ID: $($taskResponse.id)" -ForegroundColor Green
    Write-Host "任务详情:" -ForegroundColor Cyan
    Write-Host "  问题编号: $($taskResponse.issue_no)"
    Write-Host "  问题摘要: $($taskResponse.issue_summary)"
    Write-Host "  处理人: $($taskResponse.assigned_to)"
    Write-Host "  优先级: $($taskResponse.priority)"
    Write-Host "  提交人: $($taskResponse.submitter)"
    Write-Host "  基线: $($taskResponse.baseline)"
    Write-Host "  任务状态: $($taskResponse.task_status)"
    Write-Host "  滞留天数: $($taskResponse.delay_days)"
    Write-Host "  问题链接: $($taskResponse.issue_link)"
    Write-Host "  问题状态: $($taskResponse.issue_status)"
} catch {
    Write-Host "获取任务失败: $($_.Exception.Message)" -ForegroundColor Red
    exit 1
}

# 测试更新任务
Write-Host "`n5. 测试更新任务..." -ForegroundColor Yellow
$updateTask = @{
    issue_no = "ISSUE-001-UPDATED"
    issue_summary = "更新后的问题摘要"
    assigned_to = "王五"
    priority = "中"
    submitter = "李四"
    submitted_at = "2025-01-01T10:00:00Z"
    baseline = "v1.1.0"
    task_status = "已完成"
    delay_days = 2
    issue_link = "https://example.com/issue/001-updated"
    issue_status = "已关闭"
} | ConvertTo-Json

try {
    Invoke-RestMethod -Uri "$baseUrl/tasks?id=$taskId" -Method PUT -Body $updateTask -ContentType "application/json"
    Write-Host "任务更新成功" -ForegroundColor Green
    
    # 验证更新结果
    $updatedTask = Invoke-RestMethod -Uri "$baseUrl/tasks/$taskId" -Method GET
    Write-Host "更新后的任务详情:" -ForegroundColor Cyan
    Write-Host "  问题编号: $($updatedTask.issue_no)"
    Write-Host "  问题摘要: $($updatedTask.issue_summary)"
    Write-Host "  处理人: $($updatedTask.assigned_to)"
    Write-Host "  优先级: $($updatedTask.priority)"
    Write-Host "  任务状态: $($updatedTask.task_status)"
    Write-Host "  滞留天数: $($updatedTask.delay_days)"
    Write-Host "  基线: $($updatedTask.baseline)"
    Write-Host "  问题状态: $($updatedTask.issue_status)"
} catch {
    Write-Host "更新任务失败: $($_.Exception.Message)" -ForegroundColor Red
    exit 1
}

# 测试删除任务
Write-Host "`n6. 测试删除任务..." -ForegroundColor Yellow
try {
    Invoke-RestMethod -Uri "$baseUrl/tasks?id=$taskId" -Method DELETE
    Write-Host "任务删除成功" -ForegroundColor Green
    
    # 验证删除结果
    try {
        Invoke-RestMethod -Uri "$baseUrl/tasks/$taskId" -Method GET
        Write-Host "错误：任务应该已被删除但仍然存在" -ForegroundColor Red
    } catch {
        Write-Host "验证删除成功：任务已不存在" -ForegroundColor Green
    }
} catch {
    Write-Host "删除任务失败: $($_.Exception.Message)" -ForegroundColor Red
    exit 1
}

Write-Host "`n所有测试完成！" -ForegroundColor Green
Write-Host "新的字段结构测试通过：" -ForegroundColor Cyan
Write-Host "✓ ID - 任务ID"
Write-Host "✓ IssueNo - 问题编号"
Write-Host "✓ IssueSummary - 问题摘要"
Write-Host "✓ AssignedTo - 问题处理人"
Write-Host "✓ Priority - 问题级别"
Write-Host "✓ Submitter - 问题提交人"
Write-Host "✓ SubmittedAt - 问题提交时间"
Write-Host "✓ Baseline - 问题基线"
Write-Host "✓ TaskStatus - 任务状态"
Write-Host "✓ DelayDays - 任务滞留天数"
Write-Host "✓ IssueLink - 问题链接"
Write-Host "✓ IssueStatus - 问题状态"
