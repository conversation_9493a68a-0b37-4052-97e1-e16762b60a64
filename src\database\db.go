package database

import (
	"database/sql"
	"log"

	_ "github.com/mattn/go-sqlite3"
)

var DB *sql.DB

func InitDB() {
	var err error
	// 使用SQLite内存模式，数据仅存在于内存中，应用重启后数据不保留
	// 添加cache=shared参数允许多个连接共享同一个内存数据库
	DB, err = sql.Open("sqlite3", ":memory:?cache=shared")
	if err != nil {
		log.Fatalf("Failed to connect to in-memory database: %v", err)
	}

	// 配置连接池以优化内存使用和性能
	DB.SetMaxOpenConns(10)   // 限制最大连接数
	DB.SetMaxIdleConns(5)    // 限制空闲连接数
	DB.SetConnMaxLifetime(0) // 连接不过期，因为是内存数据库

	// 测试数据库连接
	if err = DB.Ping(); err != nil {
		log.Fatalf("Failed to ping in-memory database: %v", err)
	}

	log.Println("Successfully connected to SQLite in-memory database")

	// 创建表结构
	createTableQuery := `CREATE TABLE IF NOT EXISTS tasks (
		id INTEGER PRIMARY KEY AUTOINCREMENT,
		issue_no TEXT NOT NULL,
		issue_summary TEXT NOT NULL,
		assigned_to TEXT,
		priority TEXT,
		submitter TEXT,
		submitted_at DATETIME DEFAULT CURRENT_TIMESTAMP,
		baseline TEXT,
		task_status TEXT NOT NULL,
		delay_days INTEGER DEFAULT 0,
		issue_link TEXT,
		issue_status TEXT
	)`

	_, err = DB.Exec(createTableQuery)
	if err != nil {
		log.Fatalf("Failed to create tasks table: %v", err)
	}

	log.Println("Tasks table created successfully in memory")

	// 可选：创建索引以优化查询性能
	createIndexQuery := `CREATE INDEX IF NOT EXISTS idx_tasks_task_status ON tasks(task_status)`
	_, err = DB.Exec(createIndexQuery)
	if err != nil {
		log.Printf("Warning: Failed to create task_status index: %v", err)
	}

	createIndexQuery2 := `CREATE INDEX IF NOT EXISTS idx_tasks_submitted_at ON tasks(submitted_at)`
	_, err = DB.Exec(createIndexQuery2)
	if err != nil {
		log.Printf("Warning: Failed to create submitted_at index: %v", err)
	}

	createIndexQuery3 := `CREATE INDEX IF NOT EXISTS idx_tasks_assigned_to ON tasks(assigned_to)`
	_, err = DB.Exec(createIndexQuery3)
	if err != nil {
		log.Printf("Warning: Failed to create assigned_to index: %v", err)
	}

	log.Println("Database indexes created successfully")
}

// CloseDB 关闭数据库连接
func CloseDB() {
	if DB != nil {
		if err := DB.Close(); err != nil {
			log.Printf("Error closing database: %v", err)
		} else {
			log.Println("Database connection closed successfully")
		}
	}
}

// GetDBStats 获取数据库连接池统计信息（用于监控内存使用）
func GetDBStats() sql.DBStats {
	if DB != nil {
		return DB.Stats()
	}
	return sql.DBStats{}
}
