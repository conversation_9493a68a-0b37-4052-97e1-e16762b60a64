package main

import (
	"context"
	"fmt"
	"idms_task_server/database"
	"idms_task_server/handlers"
	"log"
	"os"
	"os/signal"
	"syscall"
	"time"

	"github.com/labstack/echo/v4"
	"github.com/labstack/echo/v4/middleware"
)

func main() {
	fmt.Println("Starting Task Management Server...")
	log.Println("Starting Task Management Server...")

	// 初始化内存数据库
	fmt.Println("Initializing database...")
	database.InitDB()
	fmt.Println("Database initialized successfully")

	// 设置优雅关闭
	defer func() {
		log.Println("Shutting down server...")
		database.CloseDB()
		log.Println("Server shutdown complete")
	}()

	// 创建Echo实例
	e := echo.New()

	// 添加中间件
	e.Use(middleware.Logger())
	e.Use(middleware.Recover())
	e.Use(middleware.CORS())

	// 注册路由
	e.POST("/tasks", handlers.CreateTask)
	e.GET("/tasks", handlers.GetTasks)
	e.GET("/tasks/:id", handlers.GetTaskByID)
	e.PUT("/tasks", handlers.UpdateTask)
	e.DELETE("/tasks", handlers.DeleteTask)

	// 添加健康检查端点
	e.GET("/health", func(c echo.Context) error {
		stats := database.GetDBStats()
		return c.JSON(200, map[string]interface{}{
			"status":   "healthy",
			"database": "in-memory SQLite",
			"db_stats": map[string]interface{}{
				"open_connections": stats.OpenConnections,
				"in_use":           stats.InUse,
				"idle":             stats.Idle,
			},
		})
	})

	// 启动服务器的goroutine
	go func() {
		log.Println("Server starting on :8080...")
		if err := e.Start(":8080"); err != nil {
			log.Printf("Server error: %v", err)
		}
	}()

	// 等待中断信号以优雅关闭服务器
	quit := make(chan os.Signal, 1)
	signal.Notify(quit, os.Interrupt, syscall.SIGTERM)
	<-quit

	log.Println("Received shutdown signal...")

	// 创建超时上下文用于优雅关闭
	ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
	defer cancel()

	// 优雅关闭服务器
	if err := e.Shutdown(ctx); err != nil {
		log.Printf("Server forced to shutdown: %v", err)
	}
}
