package models

import "time"

type Task struct {
	ID           int       `json:"id"`
	IssueNo      string    `json:"issue_no"`
	IssueSummary string    `json:"issue_summary"`
	AssignedTo   string    `json:"assigned_to"`
	Priority     string    `json:"priority"`
	Submitter    string    `json:"submitter"`
	SubmittedAt  time.Time `json:"submitted_at"`
	Baseline     string    `json:"baseline"`
	TaskStatus   string    `json:"task_status"`
	DelayDays    int       `json:"delay_days"`
	IssueLink    string    `json:"issue_link"`
	IssueStatus  string    `json:"issue_status"`
}
