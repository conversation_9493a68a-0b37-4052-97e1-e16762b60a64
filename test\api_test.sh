#!/bin/bash

# Task Management API 测试脚本
# 测试新的字段结构

echo "开始测试 Task Management API..."

# 设置API基础URL
BASE_URL="http://localhost:8080"

# 颜色定义
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# 测试健康检查
echo -e "\n${YELLOW}1. 测试健康检查...${NC}"
HEALTH_RESPONSE=$(curl -s "$BASE_URL/health")
if [ $? -eq 0 ]; then
    echo -e "${GREEN}健康检查成功${NC}"
    echo -e "${CYAN}响应: $HEALTH_RESPONSE${NC}"
else
    echo -e "${RED}健康检查失败${NC}"
    exit 1
fi

# 测试创建任务
echo -e "\n${YELLOW}2. 测试创建任务...${NC}"
CREATE_RESPONSE=$(curl -s -X POST "$BASE_URL/tasks" \
    -H "Content-Type: application/json" \
    -d '{
        "issue_no": "ISSUE-001",
        "issue_summary": "测试问题摘要",
        "assigned_to": "张三",
        "priority": "高",
        "submitter": "李四",
        "submitted_at": "2025-01-01T10:00:00Z",
        "baseline": "v1.0.0",
        "task_status": "进行中",
        "delay_days": 0,
        "issue_link": "https://example.com/issue/001",
        "issue_status": "打开"
    }')

if [ $? -eq 0 ]; then
    echo -e "${GREEN}任务创建成功${NC}"
    echo -e "${CYAN}响应: $CREATE_RESPONSE${NC}"
    
    # 提取任务ID
    TASK_ID=$(echo $CREATE_RESPONSE | grep -o '"id":[0-9]*' | grep -o '[0-9]*')
    echo -e "${GREEN}任务ID: $TASK_ID${NC}"
else
    echo -e "${RED}创建任务失败${NC}"
    exit 1
fi

# 测试获取所有任务
echo -e "\n${YELLOW}3. 测试获取所有任务...${NC}"
ALL_TASKS_RESPONSE=$(curl -s "$BASE_URL/tasks")
if [ $? -eq 0 ]; then
    echo -e "${GREEN}获取任务列表成功${NC}"
    echo -e "${CYAN}响应: $ALL_TASKS_RESPONSE${NC}"
else
    echo -e "${RED}获取任务列表失败${NC}"
    exit 1
fi

# 测试根据ID获取任务
echo -e "\n${YELLOW}4. 测试根据ID获取任务...${NC}"
TASK_RESPONSE=$(curl -s "$BASE_URL/tasks/$TASK_ID")
if [ $? -eq 0 ]; then
    echo -e "${GREEN}获取任务成功${NC}"
    echo -e "${CYAN}响应: $TASK_RESPONSE${NC}"
else
    echo -e "${RED}获取任务失败${NC}"
    exit 1
fi

# 测试更新任务
echo -e "\n${YELLOW}5. 测试更新任务...${NC}"
UPDATE_RESPONSE=$(curl -s -X PUT "$BASE_URL/tasks?id=$TASK_ID" \
    -H "Content-Type: application/json" \
    -d '{
        "issue_no": "ISSUE-001-UPDATED",
        "issue_summary": "更新后的问题摘要",
        "assigned_to": "王五",
        "priority": "中",
        "submitter": "李四",
        "submitted_at": "2025-01-01T10:00:00Z",
        "baseline": "v1.1.0",
        "task_status": "已完成",
        "delay_days": 2,
        "issue_link": "https://example.com/issue/001-updated",
        "issue_status": "已关闭"
    }')

if [ $? -eq 0 ]; then
    echo -e "${GREEN}任务更新成功${NC}"
    
    # 验证更新结果
    UPDATED_TASK=$(curl -s "$BASE_URL/tasks/$TASK_ID")
    echo -e "${CYAN}更新后的任务: $UPDATED_TASK${NC}"
else
    echo -e "${RED}更新任务失败${NC}"
    exit 1
fi

# 测试删除任务
echo -e "\n${YELLOW}6. 测试删除任务...${NC}"
DELETE_RESPONSE=$(curl -s -X DELETE "$BASE_URL/tasks?id=$TASK_ID")
if [ $? -eq 0 ]; then
    echo -e "${GREEN}任务删除成功${NC}"
    
    # 验证删除结果
    DELETED_CHECK=$(curl -s "$BASE_URL/tasks/$TASK_ID")
    if [[ $DELETED_CHECK == *"Task not found"* ]]; then
        echo -e "${GREEN}验证删除成功：任务已不存在${NC}"
    else
        echo -e "${RED}错误：任务应该已被删除但仍然存在${NC}"
    fi
else
    echo -e "${RED}删除任务失败${NC}"
    exit 1
fi

echo -e "\n${GREEN}所有测试完成！${NC}"
echo -e "${CYAN}新的字段结构测试通过：${NC}"
echo -e "✓ ID - 任务ID"
echo -e "✓ IssueNo - 问题编号"
echo -e "✓ IssueSummary - 问题摘要"
echo -e "✓ AssignedTo - 问题处理人"
echo -e "✓ Priority - 问题级别"
echo -e "✓ Submitter - 问题提交人"
echo -e "✓ SubmittedAt - 问题提交时间"
echo -e "✓ Baseline - 问题基线"
echo -e "✓ TaskStatus - 任务状态"
echo -e "✓ DelayDays - 任务滞留天数"
echo -e "✓ IssueLink - 问题链接"
echo -e "✓ IssueStatus - 问题状态"
