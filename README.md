# Task Management REST API

This project is a Golang-based HTTP REST API backend service designed to support IntelliJ IDEA's Task feature. It provides CRUD operations for tasks, task status management, and task list querying and filtering.

## Features
- Create, Read, Update, and Delete tasks
- Manage task statuses (e.g., To-Do, In Progress, Completed)
- Query and filter task lists

## Technologies Used
- Go
- SQLite3
- RESTful API standards

## Project Structure
- `main.go`: Entry point of the application
- `handlers/`: HTTP request handlers
- `models/`: Database models
- `database/`: Database connection and operations
- `migrations/`: Database initialization and migration scripts
- `utils/`: Utility functions

## Setup
1. Install Go and SQLite3.
2. Clone the repository.
3. Run the application:
   ```bash
   go run main.go
   ```

## API Endpoints
- `POST /tasks`: Create a new task
- `GET /tasks`: Retrieve all tasks
- `GET /tasks/:id`: Retrieve a task by ID
- `PUT /tasks?id={id}`: Update a task by ID
- `DELETE /tasks?id={id}`: Delete a task by ID

## Example Usage
### Create a Task
```bash
curl -X POST -H "Content-Type: application/json" -d '{"title":"New Task","description":"Task description","status":"To-Do"}' http://localhost:8080/tasks
```

### Get All Tasks
```bash
curl -X GET http://localhost:8080/tasks
```

### Get Task by ID
```bash
curl -X GET http://localhost:8080/tasks/1
```

### Update a Task
```bash
curl -X PUT -H "Content-Type: application/json" -d '{"title":"Updated Task","description":"Updated description","status":"In Progress"}' http://localhost:8080/tasks?id=1
```

### Delete a Task
```bash
curl -X DELETE http://localhost:8080/tasks?id=1
```
