# 快速启动指南

## 新字段结构概述

Task结构体已成功更新为包含以下12个字段：

✅ **ID** - 任务ID (int)
✅ **IssueNo** - 问题编号 (string)
✅ **IssueSummary** - 问题摘要 (string)
✅ **AssignedTo** - 问题处理人 (string)
✅ **Priority** - 问题级别 (string)
✅ **Submitter** - 问题提交人 (string)
✅ **SubmittedAt** - 问题提交时间 (time.Time)
✅ **Baseline** - 问题基线 (string)
✅ **TaskStatus** - 任务状态 (string)
✅ **DelayDays** - 任务滞留天数 (int)
✅ **IssueLink** - 问题链接 (string)
✅ **IssueStatus** - 问题状态 (string)

## 启动服务器

### 方法1: 直接运行源码
```bash
cd src
go run main.go
```

### 方法2: 使用编译后的可执行文件
```bash
# 编译（如果还没编译）
cd src
go build -o ../build/idms_task_server.exe .

# 运行
cd ..
./build/idms_task_server.exe
```

服务器将在 `http://localhost:8080` 启动

## 测试新的API

### 健康检查
```bash
curl http://localhost:8080/health
```

### 创建任务
```bash
curl -X POST http://localhost:8080/tasks \
  -H "Content-Type: application/json" \
  -d '{
    "issue_no": "IDMS-2025-001",
    "issue_summary": "系统登录功能优化",
    "assigned_to": "张三",
    "priority": "高",
    "submitter": "李四",
    "submitted_at": "2025-01-01T09:30:00Z",
    "baseline": "v2.1.0",
    "task_status": "进行中",
    "delay_days": 3,
    "issue_link": "https://jira.company.com/browse/IDMS-2025-001",
    "issue_status": "In Progress"
  }'
```

### 获取所有任务
```bash
curl http://localhost:8080/tasks
```

### 获取特定任务
```bash
curl http://localhost:8080/tasks/1
```

### 更新任务
```bash
curl -X PUT http://localhost:8080/tasks?id=1 \
  -H "Content-Type: application/json" \
  -d '{
    "issue_no": "IDMS-2025-001-UPDATED",
    "issue_summary": "更新后的问题摘要",
    "assigned_to": "王五",
    "priority": "中",
    "submitter": "李四",
    "submitted_at": "2025-01-01T09:30:00Z",
    "baseline": "v2.2.0",
    "task_status": "已完成",
    "delay_days": 5,
    "issue_link": "https://jira.company.com/browse/IDMS-2025-001",
    "issue_status": "Closed"
  }'
```

### 删除任务
```bash
curl -X DELETE http://localhost:8080/tasks?id=1
```

## 自动化测试

运行完整的API测试套件：

### PowerShell (Windows)
```powershell
./test/api_test.ps1
```

### Bash (Linux/Mac/Git Bash)
```bash
chmod +x ./test/api_test.sh
./test/api_test.sh
```

## 文件结构

```
idms_task_server/
├── src/
│   ├── models/task.go          # ✅ 更新的Task结构体
│   ├── handlers/task_handler.go # ✅ 更新的API处理器
│   ├── database/db.go          # ✅ 更新的数据库配置
│   ├── migrations/init.sql     # ✅ 更新的数据库表结构
│   └── main.go                 # 主程序入口
├── build/
│   └── idms_task_server.exe    # 编译后的可执行文件
├── test/
│   ├── api_test.ps1           # PowerShell测试脚本
│   ├── api_test.sh            # Bash测试脚本
│   └── sample_task.json       # 示例任务数据
└── doc/
    ├── NEW_TASK_STRUCTURE.md  # 详细的字段变更文档
    └── QUICK_START.md         # 本文档
```

## 重要提示

⚠️ **这是一个破坏性变更** - 与之前的API版本不兼容
⚠️ **内存数据库** - 服务器重启后数据会丢失
⚠️ **字段命名** - 使用Go语言驼峰命名规范，JSON使用下划线命名

## 下一步

1. 启动服务器
2. 运行测试脚本验证功能
3. 根据需要调整字段值和业务逻辑
4. 集成到你的应用程序中

更多详细信息请参考 `doc/NEW_TASK_STRUCTURE.md`
